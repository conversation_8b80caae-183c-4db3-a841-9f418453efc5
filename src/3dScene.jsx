import { Canvas } from '@react-three/fiber'
import { OrbitControls } from '@react-three/drei'
import { Suspense } from 'react'
import { GloomyCity } from './finalCity_V1'
import Ocean  from './Ocean'
import { Leva } from 'leva'

export default function ThreeDscene() {
  return (
   
     <Canvas 
      camera={{ position: [150, 150, 150], fov: 60 }}
      style={{ background: 'white' }}
      shadows
    >
      <Leva hidden={true} />
      <axesHelper args={[1000]} />
      {/* Sea plane aligned with Z axis */}
      <Suspense fallback={null}>
        {/* Orbit Controls - Restricted to Y axis rotation */}
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          enableDamping={true}
        />

        <ambientLight intensity={0.2}/>

        {/* City Model */}
        {/* <GloomyCity scale={0.00008} /> */}
        {/* <City scale={0.08} /> */}
        {/* <NewCity scale={0.08} /> */}
        <GloomyCity scale={0.8} position={[180, 0, 180]} />
        <Ocean />


      </Suspense>
    </Canvas>
   
  )
}
