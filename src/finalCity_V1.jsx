import * as THREE from 'three'
import { useGLTF, useAnimations, Sky, Bvh } from '@react-three/drei'
import { use<PERSON>rame, useThree } from "@react-three/fiber"
import { EffectComposer, Selection, Autofocus, Noise, Sepia, BrightnessContrast, Bloom, Vignette } from "@react-three/postprocessing"
import { easing } from "maath"
import React, { useRef, useEffect, useMemo } from 'react'
import { useControls } from 'leva'
import { BlendFunction } from 'postprocessing' // <-- Use capital B, this is the correct import

// Subtle atmospheric particles for added realism
const AtmosphericParticles = () => {
  const particlesRef = useRef()
  const particleCount = 80 // Reduced for subtlety

  const particles = useMemo(() => {
    const positions = new Float32Array(particleCount * 3)
    const velocities = new Float32Array(particleCount * 3)

    for (let i = 0; i < particleCount; i++) {
      // Spread particles around the city area
      positions[i * 3] = (Math.random() - 0.5) * 400 + 180     // x
      positions[i * 3 + 1] = Math.random() * 100 + 10          // y
      positions[i * 3 + 2] = (Math.random() - 0.5) * 400 + 180 // z

      // Slow floating motion
      velocities[i * 3] = (Math.random() - 0.5) * 0.1      // x velocity
      velocities[i * 3 + 1] = Math.random() * 0.05 + 0.01  // y velocity (upward)
      velocities[i * 3 + 2] = (Math.random() - 0.5) * 0.1  // z velocity
    }

    return { positions, velocities }
  }, [])

  useFrame((state, delta) => {
    if (particlesRef.current) {
      const positions = particlesRef.current.geometry.attributes.position.array

      for (let i = 0; i < particleCount; i++) {
        // Update positions based on velocities
        positions[i * 3] += particles.velocities[i * 3] * delta * 10
        positions[i * 3 + 1] += particles.velocities[i * 3 + 1] * delta * 10
        positions[i * 3 + 2] += particles.velocities[i * 3 + 2] * delta * 10

        // Reset particles that float too high or drift too far
        if (positions[i * 3 + 1] > 120) {
          positions[i * 3 + 1] = 10
        }
        if (Math.abs(positions[i * 3] - 180) > 250) {
          positions[i * 3] = (Math.random() - 0.5) * 400 + 180
        }
        if (Math.abs(positions[i * 3 + 2] - 180) > 250) {
          positions[i * 3 + 2] = (Math.random() - 0.5) * 400 + 180
        }
      }

      particlesRef.current.geometry.attributes.position.needsUpdate = true
    }
  })

  return (
    <points ref={particlesRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particleCount}
          array={particles.positions}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        size={0.4}
        color="#ffffff"
        transparent
        opacity={0.1}
        sizeAttenuation
        blending={THREE.AdditiveBlending}
      />
    </points>
  )
}

export const Effects = () => {
  // Blend function controls for each effect
  const blendOptions = [
    "NORMAL",
    "ADD",
    "ALPHA",
    "AVERAGE",
    "COLOR_BURN",
    "COLOR_DODGE",
    "DARKEN",
    "DIFFERENCE",
    "EXCLUSION",
    "LIGHTEN",
    "MULTIPLY",
    "DIVIDE",
    "NEGATION",
    "NORMAL",
    "OVERLAY",
    "REFLECT",
    "SCREEN",
    "SOFT_LIGHT",
    "SUBTRACT"
  ];

  // Example: Add blend function control for Bloom and Sepia
  const bloomBlend = useControls("bloom", {
    blendFunction: { value: "SCREEN", options: blendOptions }
  }).blendFunction;

  const sepiaBlend = useControls("sepia", {
    blendFunction: { value: "COLOR_DODGE", options: blendOptions }
  }).blendFunction;

  const vignetteBlend = useControls("vignette", {
    blendFunction: { value: "MULTIPLY", options: blendOptions }
  }).blendFunction;

  const autofocusConfig = useControls("autofocus", {
    enabled: true,
    mouse: true,
    focusRange: { value: 0.001, min: 0, max: 0.01 },
    bokehScale: { value: 8, min: 0, max: 40 },
    focalLength: { value: 0.8, min: 0, max: 1 },
    smoothTime: { value: 0.5, min: 0, max: 1 },
  });

  const noiseConfig = useControls("noise", {
    enabled: false, // Disabled - too much for realistic look
    opacity: { value: 0.15, min: 0, max: 1 },
  });

  const brightnessContrastConfig = useControls("brightnessContrast", {
    enabled: true, // Enabled for subtle enhancement
    brightness: { value: 0.05, min: 0, max: 1 }, // Very subtle brightness
    contrast: { value: 0.6, min: 0, max: 1 }, // Light contrast boost
  });

  const bloomConfig = useControls("bloom", {
    enabled: true,
    intensity: { value: 0.4, min: 0, max: 1 }, // Enhanced glow
    radius: { value: 0.8, min: 0, max: 1 }, // Wider bloom spread
    threshold: { value: 0.6, min: 0, max: 1 }, // Lower threshold for more bloom
    luminanceSmoothing: { value: 0.9, min: 0, max: 1 },
  });

  const sepiaConfig = useControls("sepia", {
    enabled: false, // Disabled for natural colors
    intensity: { value: 0.2, min: 0, max: 1 },
  });

  const vignetteConfig = useControls("vignette", {
    enabled: true, // Enabled for subtle edge darkening
    intensity: { value: 0.2, min: 0, max: 1 }, // Very subtle vignette
    blur: { value: 0.8, min: 0, max: 1 }, // Soft edges
  });

  return (
    <EffectComposer disableNormalPass>
      {/* Subtle effects for enhanced realism */}
      {brightnessContrastConfig.enabled && <BrightnessContrast {...brightnessContrastConfig} />}
      {bloomConfig.enabled && (
        <Bloom
          {...bloomConfig}
          blendFunction={BlendFunction[bloomBlend]}
        />
      )}
      {vignetteConfig.enabled && (
        <Vignette
          {...vignetteConfig}
          blendFunction={BlendFunction[vignetteBlend]}
        />
      )}
    </EffectComposer>
  );
};

export function GloomyCity(props) {
    const group = useRef()
    const { scene, animations } = useGLTF('/v3_city_v0.21.glb')
    const { actions } = useAnimations(animations, group)
    const { gl, scene: r3fScene } = useThree()

    useEffect(() => {
        Object.values(actions).forEach(action => action.play())
    }, [actions])

    useEffect(() => {
        // Enhanced atmospheric fog for more realistic depth
        r3fScene.fog = new THREE.FogExp2(0x1a1a2e, 0.0008) // Slightly denser, warmer fog
        gl.setClearColor(0x1a1a2e)
        return () => {
            r3fScene.fog = null
        }
    }, [r3fScene, gl])

    return (
        <>
            {/* Enhanced ambient lighting for more natural look */}
            <ambientLight intensity={0.15} color="#404040" />
            <hemisphereLight
                skyColor="#87CEEB"
                groundColor="#362d1a"
                intensity={0.25}
            />

            {/* Main directional light (sun/moon) */}
            <directionalLight
                position={[100, 200, 100]}
                intensity={0.35}
                color="#8888aa"
                castShadow
                shadow-mapSize-width={2048}
                shadow-mapSize-height={2048}
                shadow-camera-far={500}
                shadow-camera-left={-200}
                shadow-camera-right={200}
                shadow-camera-top={200}
                shadow-camera-bottom={-200}
            />

            {/* Street lighting - scattered point lights */}
            <pointLight position={[150, 15, 150]} intensity={0.7} color="#ffaa44" distance={80} decay={2} />
            <pointLight position={[200, 12, 180]} intensity={0.55} color="#ffaa44" distance={60} decay={2} />
            <pointLight position={[160, 18, 200]} intensity={0.6} color="#ffaa44" distance={70} decay={2} />
            <pointLight position={[180, 14, 160]} intensity={0.5} color="#ffaa44" distance={50} decay={2} />
            <pointLight position={[220, 16, 200]} intensity={0.75} color="#ffaa44" distance={85} decay={2} />

            {/* Building lights - cooler tones */}
            <pointLight position={[170, 25, 170]} intensity={0.35} color="#6699ff" distance={40} decay={2} />
            <pointLight position={[190, 30, 190]} intensity={0.3} color="#6699ff" distance={35} decay={2} />
            <pointLight position={[210, 22, 170]} intensity={0.4} color="#6699ff" distance={45} decay={2} />

            {/* Focused spot lights for key areas */}
            <spotLight
                position={[180, 50, 180]}
                target-position={[180, 0, 180]}
                angle={0.3}
                penumbra={0.5}
                intensity={0.5}
                color="#ffffff"
                distance={100}
                decay={2}
                castShadow
            />

            {/* Atmospheric rim lighting */}
            <spotLight
                position={[120, 40, 120]}
                target-position={[180, 0, 180]}
                angle={0.8}
                penumbra={0.8}
                intensity={0.18}
                color="#ff6644"
                distance={200}
                decay={1.5}
            />

            <Sky turbidity={10} rayleigh={2} mieCoefficient={0.1} mieDirectionalG={0.8} inclination={0.6} azimuth={0.25} />

            {/* Add atmospheric particles for realism */}
            <AtmosphericParticles />

            <Bvh firstHitOnly>
                <Selection>
                     <Effects />
                    <group ref={group} {...props} dispose={null}>
                        <primitive object={scene} />
                    </group>
                </Selection>
            </Bvh>
        </>
    )
}